import { ReactNode } from "react";
import { <PERSON><PERSON> } from "@/common/components/organisms";
import { Metadata } from "next";
import { ProjectProvider } from "@/common/hooks/useProjects";
import { Toaster } from "@/common/components/molecules/toast/index";

export const metadata: Metadata = {
  title: 'MediaPilot Dashboard',
  alternates: {
    canonical: `https://mediapilot.app/dashboard`,
  },
  openGraph: {
    title: 'MediaPilot Dashboard',
    url: `https://mediapilot.app/dashboard`,
  },
  twitter: {
    title: 'MediaPilot Dashboard',
  },
}

export default function Layout ({
  children,
}: {
  children: ReactNode;
}) {
  return (
    <div className="relative min-h-screen pt-16">
      <Header />
      <ProjectProvider>
        {children}
      </ProjectProvider>
      <Toaster />
    </div>
  )
}
