'use client'

import {
  useState, useMemo, useCallback, useRef, useEffect,
} from 'react';
import { useRouter } from 'next/navigation';
import {
  useProjects, useSupabaseAuth, usePosts,
} from '@/common/hooks';
import {
  But<PERSON>, Link,
} from '@/common/components/atoms';
import {
  FacebookIcon, InstagramIcon, KnowledgeBaseIcon, LinkedInColorIcon, PlusIcon, XIcon, YoutubeIcon,
} from '@/common/components/icons';
import { formatDistanceToNow } from 'date-fns';
import { basicPlanLink } from '@/common/constants';
import { ProjectModal } from '@/common/components/organisms/modal/projectModal';
import { DashboardLayout } from '@/common/components/organisms';

import { routes } from '@/common/routes';
import dynamic from "next/dynamic";
import Image from 'next/image';

const StarsLottie = dynamic(() => import('@/common/lottie/starsLottie').then(m => m.StarsLottie), {
  ssr: false,
});

export default function OverviewPage () {
  const router = useRouter();
  const [isProjectModalOpen, setIsProjectModalOpen] = useState(false);
  const [knowledgeResources, setKnowledgeResources] = useState<any[]>([]);
  const [loadingKnowledge, setLoadingKnowledge] = useState<boolean>(false);
  const { user } = useSupabaseAuth();
  const {
    projects: userProjects,
    activeProject,
    setActiveProject,
    fetchProjects,
  } = useProjects();

  const connectedAccounts = useMemo(() => {
    if (!activeProject?.accounts) {
      return [];
    }
    return activeProject.accounts.filter(account => account.connected);
  }, [activeProject?.accounts]);

  const postsAccounts = useMemo(() => {
    if (connectedAccounts.length === 0) {
      return [];
    }
    return connectedAccounts.map((account: {agentId: string | null; platform: string; connected: boolean}) => ({
      agentId: account.agentId || null,
      platform: account.platform,
      connected: account.connected,
    }));
  }, [connectedAccounts]);

  const isFirstRender = useRef(true);

  const {
    allPosts,
    isLoading: postsLoading,
    error: postsError,
  } = usePosts(postsAccounts);

  const upcomingPosts = useMemo(() => {
    if (isFirstRender.current) {
      isFirstRender.current = false;
    }


    if (!allPosts || allPosts.length === 0) {
      return [];
    }

    const processed = allPosts
      .map(post => ({
        id: Number(post.id) || Math.random(),
        platform: post.platform,
        attachments: post.attachments,
        content: post.content,
        scheduledFor: post.scheduledTime.toString(),
        status: post.status.toLowerCase() as 'posted' | 'approved',
      }))
      .sort((a, b) => new Date(a.scheduledFor).getTime() - new Date(b.scheduledFor).getTime())
      .filter(post => post.status !== 'posted')
      .slice(0, 5);
    return processed;
  }, [allPosts]);

  const isPlanEnded = activeProject?.billing_status === 'free-ended';
  const hasConnectedAccounts = activeProject?.accounts?.some(account => account.connected) || false;

  const fetchKnowledgeResources = useCallback(async () => {
    if (!activeProject?.project_id) {
      return;
    }

    setLoadingKnowledge(true);
    try {
      const agentBaseUrl = process.env.NEXT_PUBLIC_AGENT_URL || "http://localhost:2151";
      const response = await fetch(`${agentBaseUrl}/files/project/${activeProject.project_id}`);

      if (!response.ok) {
        throw new Error('Failed to fetch project files');
      }

      const data = await response.json();

      if (!data.success) {
        throw new Error(data.message || 'Failed to fetch project files');
      }

      setKnowledgeResources(data.files || []);
    } catch (error) {
      console.error('Error fetching knowledge resources:', error);
    } finally {
      setLoadingKnowledge(false);
    }
  }, [activeProject?.project_id]);

  useEffect(() => {
    if (activeProject?.project_id) {
      fetchKnowledgeResources();
    }
  }, [activeProject?.project_id, fetchKnowledgeResources]);

  const getIconByPlatform = (platform: string) => {
    switch (platform.toLowerCase()) {
    case 'twitter':
      return <XIcon />;
    case 'linkedin':
      return <LinkedInColorIcon />;
    case 'facebook':
      return <FacebookIcon />;
    case 'instagram':
      return <InstagramIcon />;
    case 'youtube':
      return <YoutubeIcon />;
    default:
      return null;
    }
  };
  const handleCreatePost = () => {
    const url = routes.dashboardCalendarSchedulePath;
    router.push(url);
  };
  return (
    <DashboardLayout>
      {activeProject && (
        <div className="space-y-8">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <div>
              <h1 className="text-2xl md:text-3xl font-bold text-white">Dashboard</h1>
              <p className="text-gray-400 mt-1">Manage your social media presence</p>
            </div>
            {hasConnectedAccounts && (
              <Button
                variant="gradient"
                size="sm"
                type="button"
                className="flex items-center gap-2"
                onClick={handleCreatePost}
              >
                <PlusIcon className="w-4 h-4" />
                <span>Create Post</span>
              </Button>
            )}
          </div>

          <div className="bg-violets-are-blue/5 border border-white/5 rounded-3xl p-6">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-semibold text-white">Your Projects</h2>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {userProjects.length > 0 ? (
                userProjects.map((project) => {
                  const isSelected = activeProject?.project_id === project.project_id;
                  const isProjectBasic = project.billing_status === 'basic';
                  return (
                    <button
                      key={project.project_id}
                      className={`w-full text-left bg-white/5 border rounded-2xl p-4 transition-all duration-200
                      ${isSelected
                      ? 'border-emerald-500 bg-gradient-to-tr from-emerald-500/10 to-green-400/5'
                      : isProjectBasic
                        ? 'border-white/10 hover:border-emerald-500/30'
                        : 'border-white/5 hover:border-white/20 opacity-70'}`}
                      onClick={() => {
                        if (project) {
                          setActiveProject(project);
                        }
                      }}
                    >
                      <div className="flex items-center gap-3">
                        <div className={`h-8 w-8 rounded-lg flex items-center justify-center
                        ${isSelected
                      ? 'bg-gradient-to-tr from-emerald-500 to-green-400'
                      : isProjectBasic
                        ? 'bg-white/10'
                        : 'bg-white/5'} text-white font-medium`}>
                          {project.name.charAt(0).toUpperCase()}
                        </div>
                        <div>
                          <h3 className={`font-medium ${isProjectBasic ? 'text-white' : 'text-gray-400'}`}>{project.name}</h3>
                        </div>
                        {isProjectBasic && (
                          <div className={`ml-auto ${isSelected ? "text-emerald-400 bg-emerald-500/20" : "text-gray-400 bg-gray-500/20"} text-xs px-2 py-1 rounded-full`}>
                            Basic
                          </div>
                        )}
                        {!isProjectBasic && (
                          <div className="ml-auto bg-gray-500/20 text-gray-400 text-xs px-2 py-1 rounded-full">
                            Free
                          </div>
                        )}
                      </div>
                    </button>
                  );
                })
              ) : (
                <div className="col-span-3 text-center py-8">
                  <p className="text-gray-400">No projects yet. Add your first project to get started.</p>
                </div>
              )}
            </div>
          </div>

          {!isPlanEnded && (
            <div className="bg-violets-are-blue/5 border border-white/5 rounded-3xl p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-semibold text-white">Your Accounts</h2>
                <Link
                  href={routes.dashboardAccountsPath}
                  variant="outline"
                  size="sm"
                  className="flex items-center gap-2"
                >
                  <span>Manage Connections</span>
                </Link>
              </div>

              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
                {activeProject?.accounts?.length ? activeProject.accounts.map((account) => (
                  <div
                    key={account.platform}
                    className={`bg-white/5 border ${account.connected ? 'border-violets-are-blue/30' : 'border-white/10'} rounded-2xl p-4 flex flex-col items-center justify-center gap-2 transition-all duration-200 hover:border-violets-are-blue/50`}
                  >
                    <div className={`p-3 rounded-full ${account.connected ? 'bg-violets-are-blue/20' : 'bg-white/5'}`}>
                      {getIconByPlatform(account.platform)}
                    </div>
                    <span className="text-sm font-medium text-white">{account.platform}</span>
                    <span className={`text-xs ${account.connected ? 'text-green-400' : 'text-gray-400'}`}>
                      {account.connected ? 'Connected' : 'Not Connected'}
                    </span>
                  </div>
                )) : null}
              </div>
            </div>
          )}

          {!isPlanEnded && (
            <div className="bg-violets-are-blue/5 border border-white/5 rounded-3xl p-6 mb-8">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-semibold text-white">Knowledge Resources</h2>
                <Link
                  href={routes.dashboardKnowledgeBasePath}
                  variant="outline"
                  size="sm"
                >
                  Manage Resources
                </Link>
              </div>

              <div className="p-4 bg-white/5 rounded-2xl">
                {loadingKnowledge ? (
                  <div className="flex items-center justify-center py-4">
                    <div className="animate-spin w-6 h-6 border-2 border-violets-are-blue border-t-transparent rounded-full"></div>
                  </div>
                ) : (
                  <div className="flex flex-col md:flex-row items-center justify-between">
                    <div className="flex items-center gap-4 mb-4 md:mb-0">
                      <div className="bg-violets-are-blue/20 p-3 rounded-full">
                        <KnowledgeBaseIcon className="text-violets-are-blue" />
                      </div>
                      <div>
                        <h3 className="text-white font-medium text-lg">
                          {knowledgeResources.length > 0
                            ? `${knowledgeResources.length} Resource${knowledgeResources.length !== 1 ? 's' : ''} Added`
                            : 'No Resources Added'}
                        </h3>
                        <p className="text-gray-400 text-sm">
                          {knowledgeResources.length > 0
                            ? 'Your agents are using these resources to enhance their responses'
                            : 'Add resources to help your agents create better content'}
                        </p>
                      </div>
                    </div>
                    {knowledgeResources.length === 0 && (
                      <Button
                        variant="gradient"
                        size="sm"
                        onClick={() => window.location.href = routes.dashboardKnowledgeBasePath}
                      >
                        Add Resources
                      </Button>
                    )}
                  </div>
                )}
              </div>
            </div>
          )}

          {!isPlanEnded && (
            <div className="bg-violets-are-blue/5 border border-white/5 rounded-3xl p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-semibold text-white">Upcoming Posts</h2>
                <Link
                  href={routes.dashboardCalendarPath}
                  variant="outline"
                  size="sm"
                >
                  View Calendar
                </Link>
              </div>

              <div className="space-y-4">
                {postsLoading && (
                  <div className="text-center py-8">
                    <p className="text-gray-400">Loading posts...</p>
                  </div>
                )}

                {postsError && !postsLoading && (
                  <div className="text-center py-8">
                    <p className="text-red-400">Error loading posts. Please try again.</p>
                  </div>
                )}

                {!postsLoading && !postsError && upcomingPosts.map((post) => (
                  <div
                    key={post.id}
                    className="bg-white/5 border border-white/10 rounded-2xl p-4 transition-all duration-200 hover:border-violets-are-blue/30"
                  >
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center gap-2">
                        <span className="text-white font-medium">{post.platform}</span>
                        <span className={`text-xs px-2 py-0.5 rounded-full ${post.status === 'posted' ? 'bg-green-500/20 text-green-400' : 'bg-yellow-500/20 text-yellow-400'}`}>
                          {post.status === 'posted' ? 'Posted' : 'Scheduled'}
                        </span>
                      </div>
                      <span className="text-gray-400 text-sm">
                        {formatDistanceToNow(new Date(post.scheduledFor), { addSuffix: true })}
                      </span>
                    </div>
                    <p className="text-gray-300 text-sm whitespace-pre-wrap" dangerouslySetInnerHTML={{ __html: post.content }}></p>
                    {post.attachments && post.attachments.length > 0 && (
                      <div className="mt-3">
                        <div className="flex items-center">
                          <Image
                            src={post.attachments[0]}
                            alt="Post attachment"
                            width={400}
                            height={200}
                            className="max-h-32 w-auto object-cover rounded-md"
                          />
                        </div>
                      </div>
                    )}
                  </div>
                ))}

                {!postsLoading && !postsError && upcomingPosts.length === 0 && (
                  <div className="text-center -mt-10">
                    <StarsLottie />
                    <p className="text-gray-400 -mt-10">No upcoming posts scheduled</p>
                  </div>
                )}
              </div>
            </div>
          )}

          {isPlanEnded && activeProject && (
            <div className="bg-violets-are-blue/5 border border-white/5 rounded-3xl p-6 flex items-center justify-center flex-col">
              <div className="flex items-center justify-between mb-2">
                <h2 className="text-xl font-semibold text-white">You are on the free plan</h2>
              </div>

              <p className="text-gray-400 mb-6">Please upgrade to the Basic plan to continue posting and unlock premium features.</p>
              {activeProject && (
                <Button
                  variant="gradient"
                  size="md"
                  onClick={() => {
                    const stripeUrl = `${basicPlanLink}?client_reference_id=${activeProject.project_id}`;
                    window.location.href = stripeUrl;
                  }}
                >
                  Upgrade Plan
                </Button>
              )}
            </div>
          )}

          {user && (
            <ProjectModal
              isOpen={isProjectModalOpen}
              onClose={() => setIsProjectModalOpen(false)}
              userId={user.id}
              onProjectCreated={() => fetchProjects(true)}
            />
          )}


        </div>
      )}
    </DashboardLayout>
  );
}
